# 沪深300成分股交易系统

本目录包含专门针对沪深300成分股的股票数据获取和选股系统。

## 文件说明

### 数据文件
- `hs300_stocks.csv` - 沪深300成分股完整信息（包含股票代码、名称等）
- `hs300_codes.txt` - 沪深300成分股代码列表（纯代码，每行一个）
- `data/` - 股票历史K线数据存储目录

### 脚本文件
- `get_hs300_stocks.py` - 获取沪深300成分股列表的脚本
- `hs300_fetch_kline.py` - 获取沪深300成分股历史K线数据
- `hs300_select_stock.py` - 基于沪深300成分股的选股脚本
- `hs300_configs.json` - 选股策略配置文件

## 使用方法

### 1. 获取沪深300成分股列表
```bash
python get_hs300_stocks.py
```
这将生成 `hs300_stocks.csv` 和 `hs300_codes.txt` 文件。

### 2. 获取历史K线数据
```bash
# 获取所有沪深300成分股的历史数据
python hs300_fetch_kline.py --out ./data --start 20200101 --end today

# 使用不同的数据源
python hs300_fetch_kline.py --datasource akshare --out ./data

# 指定并发线程数
python hs300_fetch_kline.py --workers 5 --out ./data
```

### 3. 运行选股策略
```bash
# 使用沪深300成分股运行选股
python hs300_select_stock.py --data-dir ./data --config ./hs300_configs.json --tickers hs300

# 指定特定日期
python hs300_select_stock.py --data-dir ./data --date 2025-07-29 --tickers hs300

# 使用数据目录中的所有股票
python hs300_select_stock.py --data-dir ./data --tickers all
```

## 配置说明

### hs300_configs.json
包含以下选股策略：
- **少妇战法** (BBIKDJSelector) - 基于BBI和KDJ指标的选股策略
- **补票战法** (BBIShortLongSelector) - 基于BBI短长期对比的选股策略  
- **TePu战法** (BreakoutVolumeKDJSelector) - 基于突破、成交量和KDJ的选股策略
- **填坑战法** (PeakKDJSelector) - 基于峰值和KDJ的选股策略

可以通过修改配置文件中的 `activate` 字段来启用/禁用特定策略，或调整 `params` 中的参数。

## 注意事项

1. 首次运行需要先获取沪深300成分股列表
2. 获取历史数据可能需要较长时间，建议使用合适的并发线程数
3. 选股功能需要先有相应的历史K线数据
4. 数据源选择：
   - `tushare` - 需要token，数据质量高
   - `akshare` - 免费，但可能有限制
   - `mootdx` - 通达信数据源

## 依赖库

确保安装了以下Python库：
```bash
pip install baostock pandas akshare tushare mootdx tqdm
```
