## 任务
1. 使用 baostock 库（http://baostock.com/baostock/index.php/%E6%B2%AA%E6%B7%B1300%E6%88%90%E5%88%86%E8%82%A1）获取沪深 300 成分股列表保存为 csv 文件

2. 复制一份 fetch_kline.py 为 hs300_fetch_kline.py，并修改其中的股票池为第一步获取到的股票列表，保存位置是 hs300/data 文件夹内，文件名是 [股票代码].csv

3. 复制一份 select_stock.py 为 hs300_select_stock.py，并修改其中的股票池为第一步获取到的股票列表

4. 复制一份 configs.json 为 hs300_configs.json，并修改其中的股票池为第一步获取到的股票列表

## 规范
1. 所有代码在 hs300 文件夹内实现
2. 数据可以从 hs300 文件夹的 data 文件夹内读取，数据文件为 [股票代码].csv

