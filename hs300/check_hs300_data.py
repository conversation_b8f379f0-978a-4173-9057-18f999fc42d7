#!/usr/bin/env python3
"""
检查根目录 data 文件夹中沪深300成分股的数据覆盖情况
如果缺少数据，则使用 fetch_kline.py 补充
"""

import sys
import logging
from pathlib import Path
from typing import List, Set
import subprocess

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger("check_hs300_data")


def get_hs300_constituents() -> List[str]:
    """获取沪深300成分股列表"""
    codes_file = Path(__file__).parent / "hs300_codes.txt"
    
    if not codes_file.exists():
        logger.error("沪深300成分股文件 %s 不存在", codes_file)
        return []
    
    try:
        with open(codes_file, "r", encoding="utf-8") as f:
            codes = [line.strip() for line in f if line.strip()]
        logger.info("加载沪深300成分股，共 %d 只股票", len(codes))
        return codes
    except Exception as e:
        logger.error("读取沪深300成分股文件失败: %s", e)
        return []


def check_data_coverage(data_dir: Path, hs300_codes: List[str]) -> tuple[Set[str], Set[str]]:
    """检查数据覆盖情况"""
    if not data_dir.exists():
        logger.warning("数据目录 %s 不存在", data_dir)
        return set(), set(hs300_codes)
    
    # 获取现有的数据文件
    existing_files = list(data_dir.glob("*.csv"))
    existing_codes = {f.stem for f in existing_files}
    
    # 计算覆盖情况
    hs300_set = set(hs300_codes)
    covered_codes = existing_codes & hs300_set
    missing_codes = hs300_set - existing_codes
    
    logger.info("数据目录: %s", data_dir.absolute())
    logger.info("总文件数: %d", len(existing_files))
    logger.info("沪深300成分股总数: %d", len(hs300_codes))
    logger.info("已有数据的沪深300股票: %d", len(covered_codes))
    logger.info("缺少数据的沪深300股票: %d", len(missing_codes))
    
    if missing_codes:
        logger.info("缺少数据的股票代码: %s", ", ".join(sorted(missing_codes)[:10]) + ("..." if len(missing_codes) > 10 else ""))
    
    return covered_codes, missing_codes


def fetch_missing_data(missing_codes: Set[str], data_dir: Path):
    """使用 fetch_kline.py 获取缺少的数据"""
    if not missing_codes:
        logger.info("所有沪深300成分股数据都已存在，无需补充")
        return
    
    logger.info("开始补充 %d 只股票的数据...", len(missing_codes))
    
    # 创建临时的股票代码文件
    temp_codes_file = Path("temp_missing_codes.txt")
    try:
        with open(temp_codes_file, "w", encoding="utf-8") as f:
            for code in sorted(missing_codes):
                f.write(f"{code}\n")
        
        # 构建 fetch_kline.py 命令
        # 注意：这里需要使用根目录的 fetch_kline.py
        root_dir = Path(__file__).parent.parent
        fetch_script = root_dir / "fetch_kline.py"
        
        if not fetch_script.exists():
            logger.error("找不到 fetch_kline.py 脚本: %s", fetch_script)
            return
        
        # 运行 fetch_kline.py 来获取缺少的数据
        # 这里我们需要修改 fetch_kline.py 或者使用其他方法来只获取指定的股票
        logger.info("使用现有的 fetch_kline.py 可能会获取所有股票数据")
        logger.info("建议手动运行以下命令来获取特定股票数据：")
        logger.info("python fetch_kline.py --out %s", data_dir.absolute())
        
    finally:
        # 清理临时文件
        if temp_codes_file.exists():
            temp_codes_file.unlink()


def main():
    """主函数"""
    # 获取沪深300成分股列表
    hs300_codes = get_hs300_constituents()
    if not hs300_codes:
        logger.error("无法获取沪深300成分股列表")
        sys.exit(1)
    
    # 检查根目录 data 文件夹
    root_dir = Path(__file__).parent.parent
    data_dir = root_dir / "data"
    
    # 检查数据覆盖情况
    covered_codes, missing_codes = check_data_coverage(data_dir, hs300_codes)
    
    # 如果有缺少的数据，提供补充建议
    if missing_codes:
        logger.info("\n=== 数据补充建议 ===")
        logger.info("发现 %d 只沪深300成分股缺少数据", len(missing_codes))
        logger.info("建议运行以下命令补充数据：")
        logger.info("cd %s", root_dir.absolute())
        logger.info("python fetch_kline.py --out ./data --start 20200101 --end today")
        logger.info("\n或者使用 akshare 数据源：")
        logger.info("python fetch_kline.py --datasource akshare --out ./data --start 20200101")
    else:
        logger.info("\n✅ 所有沪深300成分股数据都已存在！")
    
    # 生成数据覆盖报告
    report_file = Path(__file__).parent / "hs300_data_coverage_report.txt"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write("沪深300成分股数据覆盖报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"检查时间: {Path(__file__).stat().st_mtime}\n")
        f.write(f"数据目录: {data_dir.absolute()}\n")
        f.write(f"沪深300成分股总数: {len(hs300_codes)}\n")
        f.write(f"已有数据股票数: {len(covered_codes)}\n")
        f.write(f"缺少数据股票数: {len(missing_codes)}\n")
        f.write(f"数据覆盖率: {len(covered_codes)/len(hs300_codes)*100:.1f}%\n\n")
        
        if missing_codes:
            f.write("缺少数据的股票代码:\n")
            for code in sorted(missing_codes):
                f.write(f"  {code}\n")
        
        if covered_codes:
            f.write("\n已有数据的股票代码:\n")
            for code in sorted(covered_codes):
                f.write(f"  {code}\n")
    
    logger.info("数据覆盖报告已保存到: %s", report_file.absolute())


if __name__ == "__main__":
    main()
